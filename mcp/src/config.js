// Configuration for the Page Extractor MCP Server

export const CONFIG = {
  // Default base URL for the scraping service
  DEFAULT_BASE_URL: process.env.PAGE_EXTRACTOR_BASE_URL || 'http://localhost:6000',
  
  // Timeout for scraping operations (5 minutes)
  SCRAPING_TIMEOUT: parseInt(process.env.SCRAPING_TIMEOUT) || 300000,
  
  // Maximum content length to return (to avoid overwhelming the MCP client)
  MAX_CONTENT_LENGTH: parseInt(process.env.MAX_CONTENT_LENGTH) || 100000,
  
  // Server information
  SERVER_INFO: {
    name: 'page-extractor-mcp-server',
    version: '1.0.0',
    description: 'MCP server for web page extraction with streaming support',
  },
  
  // Tool definitions
  TOOLS: {
    SCRAPE_PAGE: {
      name: 'scrape_page',
      description: 'Scrape a web page and extract content as markdown. Returns immediately with the result.',
    },
    SCRAPE_PAGE_STREAM: {
      name: 'scrape_page_stream', 
      description: 'Scrape a web page with live progress updates via streaming. Shows real-time progress and returns the final result.',
    },
  },
  
  // Scraping modes
  SCRAPING_MODES: {
    NORMAL: 'normal',
    BEAST: 'beast',
  },
  
  // Progress message formatting
  PROGRESS_ICONS: {
    CONNECTED: '🔗',
    PROGRESS: '📊',
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
  },
};

export default CONFIG;
