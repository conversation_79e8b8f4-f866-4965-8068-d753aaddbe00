#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import fetch from 'node-fetch';

// Configuration
const DEFAULT_BASE_URL = 'http://localhost:6000';
const SCRAPING_TIMEOUT = 300000; // 5 minutes

// Validation schemas
const ScrapeRequestSchema = z.object({
  url: z.string().url('Invalid URL format'),
  query: z.string().optional().default(''),
  mode: z.enum(['normal', 'beast']).optional().default('normal'),
  baseUrl: z.string().url().optional().default(DEFAULT_BASE_URL),
});



class PageExtractorMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'page-extractor-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'scrape_page',
            description: 'Scrape a web page and extract content as markdown. Returns immediately with the result.',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'uri',
                  description: 'The URL to scrape',
                },
                query: {
                  type: 'string',
                  description: 'Optional query to focus the extraction on specific content',
                  default: '',
                },
                mode: {
                  type: 'string',
                  enum: ['normal', 'beast'],
                  description: 'Scraping mode: normal (faster) or beast (more thorough)',
                  default: 'normal',
                },
                baseUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'Base URL of the scraping service',
                  default: DEFAULT_BASE_URL,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'scrape_page_stream',
            description: 'Scrape a web page with live progress updates via streaming. Shows real-time progress and returns the final result.',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'uri',
                  description: 'The URL to scrape',
                },
                query: {
                  type: 'string',
                  description: 'Optional query to focus the extraction on specific content',
                  default: '',
                },
                mode: {
                  type: 'string',
                  enum: ['normal', 'beast'],
                  description: 'Scraping mode: normal (faster) or beast (more thorough)',
                  default: 'normal',
                },
                baseUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'Base URL of the scraping service',
                  default: DEFAULT_BASE_URL,
                },
              },
              required: ['url'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'scrape_page':
            return await this.handleScrapeSync(args);
          case 'scrape_page_stream':
            return await this.handleScrapeStream(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async handleScrapeSync(args) {
    const validatedArgs = ScrapeRequestSchema.parse(args);
    const { url, query, mode, baseUrl } = validatedArgs;

    try {
      const response = await fetch(`${baseUrl}/scrape-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, query, mode }),
        timeout: SCRAPING_TIMEOUT,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Scraping failed');
      }

      return {
        content: [
          {
            type: 'text',
            text: `Successfully scraped: ${url}\n\nMarkdown Content:\n${result.data.markdown}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to scrape page: ${error.message}`);
    }
  }

  async handleScrapeStream(args) {
    const validatedArgs = ScrapeRequestSchema.parse(args);
    const { url, query, mode, baseUrl } = validatedArgs;

    return new Promise(async (resolve, reject) => {
      const progressUpdates = [];
      let finalResult = null;

      try {
        // Use fetch with streaming response instead of EventSource
        const response = await fetch(`${baseUrl}/scrape`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
          },
          body: JSON.stringify({ url, query, mode }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Set up timeout
        const timeout = setTimeout(() => {
          reject(new Error('Scraping timeout'));
        }, SCRAPING_TIMEOUT);

        // Process the streaming response using async iteration
        let buffer = '';

        try {
          // Use async iteration over the response body
          for await (const chunk of response.body) {
            buffer += chunk.toString();

            // Process complete SSE messages
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep incomplete line in buffer

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));

                  switch (data.type) {
                    case 'connected':
                      progressUpdates.push(`🔗 **${data.timestamp?.split('T')[1]?.split('.')[0] || '0s'}** - ${data.message}`);
                      break;
                    case 'progress':
                      // Extract more detailed progress information
                      const progressMsg = data.message || data.step || 'Processing...';
                      const timestamp = data.timestamp?.split('T')[1]?.split('.')[0] || 'Unknown';
                      progressUpdates.push(`📊 **${timestamp}** - ${progressMsg}`);
                      break;
                    case 'completed':
                      finalResult = data.result;
                      break;
                    case 'error':
                      clearTimeout(timeout);
                      let errorProgressText = progressUpdates.join('\n');
                      reject(new Error(`Scraping failed. ## Progress Log\n${errorProgressText}\n\n**Error:** ${data.error?.message || 'Unknown error'}`));
                      return;
                    case 'done':
                      clearTimeout(timeout);

                      let doneProgressText = progressUpdates.join('\n');

                      if (finalResult && finalResult.success && finalResult.data?.markdown) {
                        resolve({
                          content: [
                            {
                              type: 'text',
                              text: `# Scraping Results for ${url}\n\n## Progress Log\n${doneProgressText}\n\n## Extracted Content\n\n${finalResult.data.markdown}`,
                            },
                          ],
                        });
                      } else if (finalResult && finalResult.success) {
                        // Handle case where scraping succeeded but no markdown was generated
                        resolve({
                          content: [
                            {
                              type: 'text',
                              text: `# Scraping Results for ${url}\n\n## Progress Log\n${doneProgressText}\n\n## Result\nScraping completed successfully but no content was extracted. This might be due to:\n- The page content being dynamically loaded\n- Access restrictions\n- The specific content not being found\n\n**Raw HTML length:** ${finalResult.data?.html?.length || 0} characters`,
                            },
                          ],
                        });
                      } else {
                        // Handle failure case with detailed progress
                        reject(new Error(`Scraping failed. ## Progress Log\n${doneProgressText}\n\n**Final Error:** ${finalResult?.error || 'No data was extracted'}`));
                      }
                      return;
                  }
                } catch (parseError) {
                  progressUpdates.push(`⚠️ Parse error: ${parseError.message}`);
                }
              }
            }
          }
        } finally {
          clearTimeout(timeout);
        }

      } catch (error) {
        reject(new Error(`Failed to start streaming scrape: ${error.message}`));
      }
    });
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Page Extractor MCP server running on stdio');
  }
}

// Start the server
const server = new PageExtractorMCPServer();
server.run().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
