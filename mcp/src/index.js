#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import fetch from 'node-fetch';

// Configuration
const DEFAULT_BASE_URL = 'http://localhost:6000';
const SCRAPING_TIMEOUT = 300000; // 5 minutes

// Validation schemas
const ScrapeRequestSchema = z.object({
  url: z.string().url('Invalid URL format'),
  query: z.string().optional().default(''),
  mode: z.enum(['normal', 'beast']).optional().default('normal'),
  baseUrl: z.string().url().optional().default(DEFAULT_BASE_URL),
});



class PageExtractorMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'page-extractor-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Store for tracking active scraping jobs
    this.activeJobs = new Map();

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'scrape_page',
            description: 'Scrape a web page and extract content as markdown. Returns immediately with the result.',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'uri',
                  description: 'The URL to scrape',
                },
                query: {
                  type: 'string',
                  description: 'Optional query to focus the extraction on specific content',
                  default: '',
                },
                mode: {
                  type: 'string',
                  enum: ['normal', 'beast'],
                  description: 'Scraping mode: normal (faster) or beast (more thorough)',
                  default: 'normal',
                },
                baseUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'Base URL of the scraping service',
                  default: DEFAULT_BASE_URL,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'scrape_page_stream',
            description: 'Scrape a web page with live progress updates via streaming. Shows real-time progress and returns the final result.',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'uri',
                  description: 'The URL to scrape',
                },
                query: {
                  type: 'string',
                  description: 'Optional query to focus the extraction on specific content',
                  default: '',
                },
                mode: {
                  type: 'string',
                  enum: ['normal', 'beast'],
                  description: 'Scraping mode: normal (faster) or beast (more thorough)',
                  default: 'normal',
                },
                baseUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'Base URL of the scraping service',
                  default: DEFAULT_BASE_URL,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'scrape_status',
            description: 'Get live progress status of an active scraping job. Use this to check progress while scraping is running.',
            inputSchema: {
              type: 'object',
              properties: {
                jobId: {
                  type: 'string',
                  description: 'Job ID returned from scrape_page_stream',
                },
              },
              required: ['jobId'],
            },
          },
          {
            name: 'list_active_jobs',
            description: 'List all currently active scraping jobs with their status and progress.',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'scrape_page':
            return await this.handleScrapeSync(args);
          case 'scrape_page_stream':
            return await this.handleScrapeStream(args);
          case 'scrape_status':
            return await this.handleScrapeStatus(args);
          case 'list_active_jobs':
            return await this.handleListActiveJobs(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async handleScrapeSync(args) {
    const validatedArgs = ScrapeRequestSchema.parse(args);
    const { url, query, mode, baseUrl } = validatedArgs;

    try {
      const response = await fetch(`${baseUrl}/scrape-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, query, mode }),
        timeout: SCRAPING_TIMEOUT,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Scraping failed');
      }

      return {
        content: [
          {
            type: 'text',
            text: `Successfully scraped: ${url}\n\nMarkdown Content:\n${result.data.markdown}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to scrape page: ${error.message}`);
    }
  }

  async handleScrapeStream(args) {
    const validatedArgs = ScrapeRequestSchema.parse(args);
    const { url, query, mode, baseUrl } = validatedArgs;

    // Generate unique job ID
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Create job tracking object
    const jobInfo = {
      id: jobId,
      url,
      query,
      mode,
      status: 'starting',
      startTime: Date.now(),
      progressUpdates: [],
      currentPhase: 'initializing',
      finalResult: null,
      lastStatusUpdate: Date.now(),
    };

    // Store job in active jobs
    this.activeJobs.set(jobId, jobInfo);

    return new Promise(async (resolve, reject) => {
      const progressUpdates = [];
      let finalResult = null;

      // Auto-status update interval (every 3 seconds)
      const statusUpdateInterval = setInterval(() => {
        this.sendLiveStatusUpdate(jobId);
      }, 3000);

      // Enhanced progress tracking with timestamps and details
      const addProgressUpdate = (message, type = 'progress', details = null, phase = null) => {
        const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
        const emoji = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'info' ? 'ℹ️' : '⚡';

        let progressEntry = `${emoji} **${timestamp}** - ${message}`;
        if (details) {
          progressEntry += `\n   ${details}`;
        }

        progressUpdates.push(progressEntry);

        // Update job info
        jobInfo.progressUpdates.push({
          timestamp: new Date().toISOString(),
          message,
          type,
          details,
          phase: phase || jobInfo.currentPhase,
        });

        if (phase) {
          jobInfo.currentPhase = phase;
        }

        if (type === 'error') {
          jobInfo.status = 'error';
        } else if (type === 'success') {
          jobInfo.status = 'completed';
        } else {
          jobInfo.status = 'running';
        }

        // Trigger immediate live update for important progress
        const importantUpdates = ['completed', 'error', 'AI analysis', 'interactive elements', 'conversion'];
        if (importantUpdates.some(keyword => message.toLowerCase().includes(keyword.toLowerCase()))) {
          // Send immediate live update for important progress
          setTimeout(() => this.sendLiveStatusUpdate(jobId), 100);
        }

        // Log to console for immediate feedback during development
        console.log(`[MCP] ${emoji} ${message}${details ? ` (${details})` : ''}`);
      };

      try {
        addProgressUpdate('Connecting to scraping service...', 'info');

        // Use fetch with streaming response instead of EventSource
        const response = await fetch(`${baseUrl}/scrape`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
          },
          body: JSON.stringify({ url, query, mode }),
        });

        addProgressUpdate('Connected to scraping service, starting process...', 'info');

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Set up timeout
        const timeout = setTimeout(() => {
          reject(new Error('Scraping timeout'));
        }, SCRAPING_TIMEOUT);

        // Process the streaming response using async iteration
        let buffer = '';

        try {
          // Use async iteration over the response body
          for await (const chunk of response.body) {
            buffer += chunk.toString();

            // Process complete SSE messages
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep incomplete line in buffer

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));

                  switch (data.type) {
                    case 'connected':
                      addProgressUpdate(data.message || 'Stream connected, starting scraping process...', 'info');
                      break;
                    case 'progress':
                      // Extract detailed progress information with context
                      const progressMsg = data.message || data.step || 'Processing...';
                      const phase = data.phase || data.action || null;
                      const elapsed = data.elapsed ? `${Math.round(data.elapsed / 1000)}s` : null;

                      let details = null;
                      if (phase && elapsed) {
                        details = `Phase: ${phase} | Elapsed: ${elapsed}`;
                      } else if (phase) {
                        details = `Phase: ${phase}`;
                      } else if (elapsed) {
                        details = `Elapsed: ${elapsed}`;
                      }

                      addProgressUpdate(progressMsg, 'progress', details);
                      break;
                    case 'info':
                      // Handle info messages from SimpleLogger (phase completions, etc.)
                      const infoMsg = data.message || 'Info update';
                      const infoResult = data.result ? `Result: ${JSON.stringify(data.result).substring(0, 100)}...` : null;
                      addProgressUpdate(infoMsg, 'info', infoResult);
                      break;
                    case 'completed':
                      // This should now only be the final completion from API handler
                      finalResult = data.result;
                      if (finalResult?.success) {
                        const contentLength = finalResult?.data?.markdown?.length || 0;
                        addProgressUpdate('Scraping completed successfully!', 'success', `Content extracted: ${contentLength} characters`);
                      } else {
                        addProgressUpdate(`Scraping failed: ${finalResult?.error || 'Unknown error'}`, 'error');
                      }
                      break;
                    case 'error':
                      // Don't immediately reject on error - wait for 'done' to see if it recovers
                      const errorMsg = data.error?.message || data.message || 'Unknown error';
                      const errorCode = data.error?.code || data.code || null;
                      const errorDetails = errorCode ? `Error Code: ${errorCode}` : null;
                      addProgressUpdate(`Scraping failed: ${errorMsg}`, 'error', errorDetails);
                      break;
                    case 'done':
                      clearTimeout(timeout);

                      let doneProgressText = progressUpdates.join('\n');

                      addProgressUpdate('Processing completed, preparing results...', 'info');

                      // Create detailed progress timeline
                      const progressTimeline = progressUpdates.join('\n');
                      const totalSteps = progressUpdates.length;
                      const successSteps = progressUpdates.filter(update => update.includes('✅')).length;
                      const errorSteps = progressUpdates.filter(update => update.includes('❌')).length;

                      // Check if we have any successful result data, regardless of intermediate errors
                      if (finalResult && finalResult.success && finalResult.data?.markdown) {
                        const processingTime = finalResult.data?.metadata?.processingTime || 0;
                        const contentLength = finalResult.data.markdown.length;

                        resolve({
                          content: [
                            {
                              type: 'text',
                              text: `# ✅ Scraping Results for ${url}

## 📊 Summary
- **Job ID**: ${jobId}
- **Status**: Success ✅
- **Content Length**: ${contentLength.toLocaleString()} characters
- **Processing Time**: ${Math.round(processingTime / 1000)}s
- **Total Steps**: ${totalSteps} (${successSteps} successful, ${errorSteps} errors)
- **Mode**: ${mode}
- **Query**: ${query || 'None'}

## 📋 Live Progress Timeline
${progressTimeline}

## 📄 Extracted Content

${finalResult.data.markdown}`,
                            },
                          ],
                        });
                      } else if (finalResult && finalResult.success) {
                        // Handle case where scraping succeeded but no markdown was generated
                        resolve({
                          content: [
                            {
                              type: 'text',
                              text: `# Scraping Results for ${url}\n\nScraping completed successfully but no content was extracted. This might be due to:\n- The page content being dynamically loaded\n- Access restrictions\n- The specific content not being found\n\n**Raw HTML length:** ${finalResult.data?.html?.length || 0} characters`,
                            },
                          ],
                        });
                      } else {
                        // Check if the progress log indicates final success despite no finalResult
                        const hasSuccessIndicators = doneProgressText.includes('✅') &&
                                                   (doneProgressText.includes('Scraping completed successfully') ||
                                                    doneProgressText.includes('HTML to Markdown conversion complete'));

                        if (hasSuccessIndicators) {
                          // Extract any content from the progress log itself
                          resolve({
                            content: [
                              {
                                type: 'text',
                                text: `# Scraping Results for ${url}\n\nScraping completed successfully based on progress indicators. The content may have been processed but not returned in the expected format.`,
                              },
                            ],
                          });
                        } else {
                          // Handle failure case with detailed progress
                          reject(new Error(`Scraping failed. ## Progress Log\n${doneProgressText}\n\n**Final Error:** ${finalResult?.error || 'No data was extracted'}`));
                        }
                      }
                      return;
                  }
                } catch (parseError) {
                  progressUpdates.push(`⚠️ Parse error: ${parseError.message}`);
                }
              }
            }
          }
        } finally {
          clearTimeout(timeout);
          clearInterval(statusUpdateInterval);
          // Clean up job from active jobs after completion
          this.activeJobs.delete(jobId);
        }

      } catch (error) {
        clearInterval(statusUpdateInterval);
        // Update job status on error
        jobInfo.status = 'error';
        jobInfo.error = error.message;
        // Clean up job from active jobs after error
        this.activeJobs.delete(jobId);
        reject(new Error(`Failed to start streaming scrape: ${error.message}`));
      }
    });
  }

  // Send live status updates automatically
  sendLiveStatusUpdate(jobId) {
    if (!this.activeJobs.has(jobId)) {
      return; // Job completed or doesn't exist
    }

    const jobInfo = this.activeJobs.get(jobId);
    const now = Date.now();

    // Only send update if there's been progress since last status update
    if (now - jobInfo.lastStatusUpdate < 2000) {
      return; // Too soon since last update
    }

    const elapsed = Math.round((now - jobInfo.startTime) / 1000);
    const progressCount = jobInfo.progressUpdates.length;
    const lastUpdate = jobInfo.progressUpdates[progressCount - 1];

    // Create live status message
    const statusEmoji = jobInfo.status === 'running' ? '🔄' : jobInfo.status === 'completed' ? '✅' : '❌';
    const phaseEmoji = jobInfo.currentPhase.includes('AI') ? '🤖' :
                      jobInfo.currentPhase.includes('browser') ? '🌐' :
                      jobInfo.currentPhase.includes('content') ? '📄' : '⚡';

    const shortJobId = jobId.split('_')[2] || 'unknown';
    const lastUpdateText = lastUpdate ? ` | Latest: ${lastUpdate.message.substring(0, 30)}...` : '';

    const liveStatusMessage = `[LIVE UPDATE] ${statusEmoji} Job ${shortJobId} | ${phaseEmoji} ${jobInfo.currentPhase} | ${elapsed}s elapsed | ${progressCount} updates${lastUpdateText}`;

    console.log(`[MCP-LIVE] ${liveStatusMessage}`);

    // Update last status time
    jobInfo.lastStatusUpdate = now;
  }

  async handleScrapeStatus(args) {
    const { jobId } = args;

    if (!this.activeJobs.has(jobId)) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ Job not found: ${jobId}\n\nThe job may have completed, failed, or never existed. Use 'list_active_jobs' to see currently running jobs.`,
          },
        ],
        isError: true,
      };
    }

    const jobInfo = this.activeJobs.get(jobId);
    const elapsed = Math.round((Date.now() - jobInfo.startTime) / 1000);
    const progressCount = jobInfo.progressUpdates.length;
    const lastUpdate = jobInfo.progressUpdates[progressCount - 1];

    return {
      content: [
        {
          type: 'text',
          text: `# 🔄 Job Status: ${jobId}

## 📊 Current Status
- **Status**: ${jobInfo.status} ${jobInfo.status === 'running' ? '🔄' : jobInfo.status === 'completed' ? '✅' : '❌'}
- **URL**: ${jobInfo.url}
- **Mode**: ${jobInfo.mode}
- **Query**: ${jobInfo.query || 'None'}
- **Current Phase**: ${jobInfo.currentPhase}
- **Elapsed Time**: ${elapsed}s
- **Progress Updates**: ${progressCount}

## 🔄 Latest Update
${lastUpdate ? `**${lastUpdate.timestamp.split('T')[1].split('.')[0]}** - ${lastUpdate.message}${lastUpdate.details ? `\n   ${lastUpdate.details}` : ''}` : 'No updates yet'}

## 💡 Next Steps
- Call this tool again to get updated progress
- Use 'list_active_jobs' to see all running jobs
- Wait for the job to complete for final results`,
        },
      ],
    };
  }

  async handleListActiveJobs() {
    const activeJobsList = Array.from(this.activeJobs.values());

    if (activeJobsList.length === 0) {
      return {
        content: [
          {
            type: 'text',
            text: `# 📋 Active Scraping Jobs

No active scraping jobs currently running.

## 💡 How to Start a Job
Use the 'scrape_page_stream' tool to start a new scraping job with live progress tracking.`,
          },
        ],
      };
    }

    const jobSummaries = activeJobsList.map(job => {
      const elapsed = Math.round((Date.now() - job.startTime) / 1000);
      const progressCount = job.progressUpdates.length;
      const statusEmoji = job.status === 'running' ? '🔄' : job.status === 'completed' ? '✅' : '❌';

      return `## ${statusEmoji} ${job.id}
- **URL**: ${job.url}
- **Status**: ${job.status}
- **Phase**: ${job.currentPhase}
- **Elapsed**: ${elapsed}s
- **Updates**: ${progressCount}
- **Mode**: ${job.mode}`;
    }).join('\n\n');

    return {
      content: [
        {
          type: 'text',
          text: `# 📋 Active Scraping Jobs (${activeJobsList.length})

${jobSummaries}

## 💡 Next Steps
- Use 'scrape_status' with a job ID to get detailed progress
- Jobs are automatically cleaned up when completed`,
        },
      ],
    };
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Page Extractor MCP server running on stdio');
  }
}

// Start the server
const server = new PageExtractorMCPServer();
server.run().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
