#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';

const DEFAULT_BASE_URL = 'http://localhost:6000';
const SCRAPING_TIMEOUT = 300000; // 5 minutes

const ScrapeRequestSchema = z.object({
  url: z.string().url(),
  query: z.string().optional().default(''),
  mode: z.enum(['normal', 'beast']).optional().default('normal'),
  baseUrl: z.string().url().optional().default(DEFAULT_BASE_URL),
});

class PageExtractorMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'page-extractor',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Store for tracking active scraping jobs
    this.activeJobs = new Map();

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'scrape_page',
            description: 'Scrape a web page and extract content as markdown. Returns immediately with the result.',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'uri',
                  description: 'The URL to scrape',
                },
                query: {
                  type: 'string',
                  description: 'Optional query to focus the extraction on specific content',
                  default: '',
                },
                mode: {
                  type: 'string',
                  enum: ['normal', 'beast'],
                  description: 'Scraping mode: normal (faster) or beast (more thorough)',
                  default: 'normal',
                },
                baseUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'Base URL of the scraping service',
                  default: DEFAULT_BASE_URL,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'scrape_page_stream',
            description: '🚀 START scraping job in background. Returns job ID immediately. Then use get_live_progress every 3-5 seconds for live updates!',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'uri',
                  description: 'The URL to scrape',
                },
                query: {
                  type: 'string',
                  description: 'Optional query to focus the extraction on specific content',
                  default: '',
                },
                mode: {
                  type: 'string',
                  enum: ['normal', 'beast'],
                  description: 'Scraping mode: normal (faster) or beast (more thorough)',
                  default: 'normal',
                },
                baseUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'Base URL of the scraping service',
                  default: DEFAULT_BASE_URL,
                },
              },
              required: ['url'],
            },
          },
          {
            name: 'get_live_progress',
            description: '🔄 GET live progress updates! Call this every 3-5 seconds to see real-time scraping progress. Returns final result when complete.',
            inputSchema: {
              type: 'object',
              properties: {
                jobId: {
                  type: 'string',
                  description: 'Job ID returned from scrape_page_stream',
                },
              },
              required: ['jobId'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'scrape_page':
            return await this.handleScrapeSync(args);
          case 'scrape_page_stream':
            return await this.handleScrapeStreamStart(args);
          case 'get_live_progress':
            return await this.handleGetLiveProgress(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async handleScrapeSync(args) {
    const validatedArgs = ScrapeRequestSchema.parse(args);
    const { url, query, mode, baseUrl } = validatedArgs;

    try {
      const response = await fetch(`${baseUrl}/scrape-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, query, mode }),
        timeout: SCRAPING_TIMEOUT,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Scraping failed');
      }

      return {
        content: [
          {
            type: 'text',
            text: `Successfully scraped: ${url}\n\nMarkdown Content:\n${result.data.markdown}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(`Failed to scrape page: ${error.message}`);
    }
  }

  async handleScrapeStreamStart(args) {
    const validatedArgs = ScrapeRequestSchema.parse(args);
    const { url, query, mode, baseUrl } = validatedArgs;

    // Generate unique job ID
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Create job tracking object
    const jobInfo = {
      id: jobId,
      url,
      query,
      mode,
      status: 'starting',
      startTime: Date.now(),
      progressUpdates: [],
      currentPhase: 'initializing',
      finalResult: null,
      lastStatusUpdate: Date.now(),
    };

    // Store job in active jobs
    this.activeJobs.set(jobId, jobInfo);

    // Start scraping in background (don't await)
    this.startBackgroundScraping(jobId, { url, query, mode, baseUrl });

    // Return immediately with job ID and instructions
    return {
      content: [
        {
          type: 'text',
          text: `# 🚀 Scraping Job Started!

## 📊 Job Details
- **Job ID**: \`${jobId}\`
- **URL**: ${url}
- **Mode**: ${mode}
- **Query**: ${query || 'None'}
- **Status**: Starting 🔄

## 🎯 NEXT STEPS - THE TRICK!
**To get live updates, call the \`get_live_progress\` tool every 3-5 seconds with this job ID:**

1. **Call**: \`get_live_progress\` with jobId: \`${jobId}\`
2. **Wait**: 3-5 seconds
3. **Repeat**: Keep calling until you get the final result!

**This is how we trick MCP's limitation - multiple tool calls = live updates!** 🎉`,
        },
      ],
    };
  }

  async handleGetLiveProgress(args) {
    const { jobId } = args;

    if (!this.activeJobs.has(jobId)) {
      return {
        content: [
          {
            type: 'text',
            text: `# ❌ Job Not Found: ${jobId}

The job may have completed, failed, or never existed.

## 💡 What to do:
- Use \`scrape_page_stream\` to start a new scraping job
- Check that you're using the correct job ID`,
          },
        ],
        isError: true,
      };
    }

    const jobInfo = this.activeJobs.get(jobId);
    const elapsed = Math.round((Date.now() - jobInfo.startTime) / 1000);
    const progressCount = jobInfo.progressUpdates.length;

    // If job is completed, return final result and clean up
    if (jobInfo.status === 'completed') {
      this.activeJobs.delete(jobId);

      const hasContent = jobInfo.finalResult?.data?.markdown;
      const contentLength = hasContent ? jobInfo.finalResult.data.markdown.length : 0;

      return {
        content: [
          {
            type: 'text',
            text: `# ✅ Scraping COMPLETED!

## 📊 Final Summary
- **Job ID**: ${jobId}
- **URL**: ${jobInfo.url}
- **Status**: Success ✅
- **Processing Time**: ${elapsed}s
- **Total Updates**: ${progressCount}
- **Content Length**: ${contentLength.toLocaleString()} characters

## 📄 Extracted Content

${hasContent ? jobInfo.finalResult.data.markdown : 'Scraping completed but no content was extracted. This may be due to the page structure or access restrictions.'}`,
          },
        ],
      };
    }

    // If job failed, return error and clean up
    if (jobInfo.status === 'error') {
      this.activeJobs.delete(jobId);

      return {
        content: [
          {
            type: 'text',
            text: `# ❌ Scraping FAILED!

## 📊 Error Summary
- **Job ID**: ${jobId}
- **URL**: ${jobInfo.url}
- **Status**: Failed ❌
- **Processing Time**: ${elapsed}s
- **Total Updates**: ${progressCount}
- **Error**: ${jobInfo.error || 'Unknown error'}

## 📋 Progress Log
${jobInfo.progressUpdates.map(update => `- ${update.message}`).join('\n')}`,
          },
        ],
        isError: true,
      };
    }

    // Return current progress - THE LIVE UPDATE!
    const statusEmoji = jobInfo.status === 'running' ? '🔄' : '⏳';
    const recentUpdates = jobInfo.progressUpdates.slice(-5).map(update =>
      `- **${update.timestamp.split('T')[1].split('.')[0]}**: ${update.message}`
    ).join('\n');

    return {
      content: [
        {
          type: 'text',
          text: `# 🔄 LIVE Progress Update #${Math.floor(elapsed/3)}

## 📊 Current Status
- **Job ID**: ${jobId}
- **Status**: ${jobInfo.status} ${statusEmoji}
- **URL**: ${jobInfo.url}
- **Current Phase**: ${jobInfo.currentPhase}
- **Elapsed Time**: ${elapsed}s
- **Progress Updates**: ${progressCount}

## 🔄 Recent Activity
${recentUpdates || 'No recent updates'}

## 💡 Keep Calling!
**Call \`get_live_progress\` again in 3-5 seconds to see more progress!**
This is how we get live updates in MCP! 🎉`,
        },
      ],
    };
  }

  // Start scraping in background without blocking the response
  async startBackgroundScraping(jobId, { url, query, mode, baseUrl }) {
    const jobInfo = this.activeJobs.get(jobId);
    if (!jobInfo) return;

    try {
      jobInfo.status = 'running';

      // Enhanced progress tracking that updates job info
      const addProgressUpdate = (message, type = 'progress', details = null, phase = null) => {
        const timestamp = new Date().toISOString();

        // Update job info
        jobInfo.progressUpdates.push({
          timestamp,
          message,
          type,
          details,
          phase: phase || jobInfo.currentPhase,
        });

        if (phase) {
          jobInfo.currentPhase = phase;
        }

        if (type === 'error') {
          jobInfo.status = 'error';
          jobInfo.error = message;
        } else if (type === 'success') {
          jobInfo.status = 'completed';
        } else {
          jobInfo.status = 'running';
        }

        // Log for debugging
        console.log(`[MCP-BG] ${message}`);
      };

      addProgressUpdate('Starting background scraping process...', 'info', null, 'connecting');

      // Use fetch with streaming response
      const response = await fetch(`${baseUrl}/scrape`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, query, mode }),
        timeout: SCRAPING_TIMEOUT,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      addProgressUpdate('Connected to scraping service, processing...', 'info', null, 'processing');

      // Handle streaming response in Node.js
      let buffer = '';
      let finalResult = null;

      // Process the response body as a stream
      response.body.on('data', (chunk) => {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;

          try {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                addProgressUpdate('Scraping process completed', 'success', null, 'completed');
                return;
              }

              const parsed = JSON.parse(data);

              if (parsed.type === 'progress') {
                addProgressUpdate(parsed.message, 'progress', parsed.details, parsed.phase);
              } else if (parsed.type === 'result') {
                finalResult = parsed;
                jobInfo.finalResult = finalResult;
                addProgressUpdate('Final result received', 'success', `Content: ${finalResult.data?.markdown?.length || 0} chars`, 'completed');
              } else if (parsed.type === 'error') {
                addProgressUpdate(parsed.message, 'error', parsed.details, 'error');
                return;
              } else if (parsed.success && parsed.data) {
                // Handle direct result format
                finalResult = parsed;
                jobInfo.finalResult = finalResult;
                addProgressUpdate('Scraping result received', 'success', `Content: ${finalResult.data?.markdown?.length || 0} chars`, 'completed');
              }
            }
          } catch (parseError) {
            addProgressUpdate(`Parse error: ${parseError.message}`, 'error', null, 'error');
          }
        }
      });

      // Wait for the response to complete
      await new Promise((resolve, reject) => {
        response.body.on('end', resolve);
        response.body.on('error', reject);
      });

      // Final status update
      if (jobInfo.status !== 'error') {
        jobInfo.status = 'completed';
        addProgressUpdate('Background scraping completed successfully', 'success', null, 'completed');
      }

    } catch (error) {
      const jobInfo = this.activeJobs.get(jobId);
      if (jobInfo) {
        jobInfo.status = 'error';
        jobInfo.error = error.message;
        jobInfo.progressUpdates.push({
          timestamp: new Date().toISOString(),
          message: `Background scraping failed: ${error.message}`,
          type: 'error',
          details: null,
          phase: 'error',
        });
      }
      console.error(`[MCP-BG] Background scraping failed for ${jobId}:`, error);
    }
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Page Extractor MCP server running on stdio');
  }
}

// Start the server
const server = new PageExtractorMCPServer();
server.run().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
