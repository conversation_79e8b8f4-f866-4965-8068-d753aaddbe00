# Integration Guide

## Quick Setup for <PERSON>

### 1. Find Your Claude Desktop Config

**macOS:**
```bash
~/Library/Application Support/Claude/claude_desktop_config.json
```

**Windows:**
```bash
%APPDATA%\Claude\claude_desktop_config.json
```

### 2. Add MCP Server Configuration

Edit your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "page-extractor": {
      "command": "node",
      "args": ["/Users/<USER>/Developer/Release/page-extracter-lamda/mcp/src/index.js"],
      "env": {
        "PAGE_EXTRACTOR_BASE_URL": "http://localhost:6000"
      }
    }
  }
}
```

**Important:** Replace the path with your actual absolute path to the MCP server.

### 3. Start Your Main Server

Before using the MCP server, make sure your main scraping server is running:

```bash
# In your main project directory
npm start
```

This should start the server on `localhost:6000`.

### 4. Restart Claude <PERSON>

After adding the configuration, restart <PERSON> to load the new MCP server.

### 5. Test the Integration

In Claude Des<PERSON>op, try:

```
Please scrape https://example.com and tell me what it contains.
```

<PERSON> should automatically use the `scrape_page` tool.

## Advanced Configuration

### Environment Variables

You can customize the MCP server behavior:

```json
{
  "mcpServers": {
    "page-extractor": {
      "command": "node",
      "args": ["/path/to/mcp/src/index.js"],
      "env": {
        "PAGE_EXTRACTOR_BASE_URL": "http://localhost:6000",
        "SCRAPING_TIMEOUT": "600000",
        "MAX_CONTENT_LENGTH": "200000"
      }
    }
  }
}
```

### Multiple Environments

You can configure different environments:

```json
{
  "mcpServers": {
    "page-extractor-local": {
      "command": "node",
      "args": ["/path/to/mcp/src/index.js"],
      "env": {
        "PAGE_EXTRACTOR_BASE_URL": "http://localhost:6000"
      }
    },
    "page-extractor-prod": {
      "command": "node", 
      "args": ["/path/to/mcp/src/index.js"],
      "env": {
        "PAGE_EXTRACTOR_BASE_URL": "https://your-production-server.com"
      }
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **MCP Server Not Loading**
   - Check the absolute path in your config
   - Ensure Node.js is in your PATH
   - Check Claude Desktop logs

2. **Connection Refused**
   - Make sure your main server is running on port 6000
   - Check if the port is accessible
   - Verify the BASE_URL configuration

3. **Timeout Errors**
   - Increase `SCRAPING_TIMEOUT` for slow websites
   - Check your internet connection
   - Try with a simpler website first

### Debug Mode

Enable debug logging by adding to your environment:

```json
{
  "env": {
    "DEBUG": "*",
    "PAGE_EXTRACTOR_BASE_URL": "http://localhost:6000"
  }
}
```

### Logs

Check Claude Desktop logs for MCP server output:

**macOS:**
```bash
tail -f ~/Library/Logs/Claude/mcp-server-page-extractor.log
```

## Testing Without Claude Desktop

You can test the MCP server directly:

```bash
cd mcp
npm test
```

Or manually test with JSON-RPC:

```bash
echo '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' | node src/index.js
```

## Performance Optimization

### For Better Performance

1. **Use Normal Mode**: Default mode is faster
2. **Specific Queries**: Use the query parameter to focus extraction
3. **Streaming**: Use `scrape_page_stream` for real-time feedback
4. **Caching**: Consider implementing caching in your main server

### Resource Management

- The MCP server is lightweight and stateless
- Each scraping request is independent
- Memory usage scales with content size
- Timeouts prevent hanging requests

## Security Considerations

1. **Local Only**: Default configuration only accepts localhost connections
2. **No Authentication**: The MCP server doesn't implement authentication
3. **URL Validation**: All URLs are validated before processing
4. **Content Limits**: Maximum content length is configurable

## Next Steps

1. **Custom Tools**: Extend the MCP server with additional tools
2. **Caching**: Add caching for frequently accessed pages
3. **Monitoring**: Add logging and metrics
4. **Deployment**: Deploy your main server for remote access
