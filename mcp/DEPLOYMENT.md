# Deployment Guide

## Production Deployment

### 1. Prerequisites

- Node.js 18.0.0 or higher
- Your main scraping server running and accessible
- MCP-compatible client (<PERSON>, etc.)

### 2. Installation

```bash
# Clone or copy the MCP folder to your desired location
cd /path/to/your/mcp/server

# Install dependencies
npm install

# Make scripts executable
chmod +x src/index.js
chmod +x setup.sh

# Test the installation
npm test
```

### 3. Configuration

#### Environment Variables

Create a `.env` file or set environment variables:

```bash
# Base URL for your scraping service
PAGE_EXTRACTOR_BASE_URL=http://localhost:6000

# Timeout for scraping operations (milliseconds)
SCRAPING_TIMEOUT=300000

# Maximum content length to return
MAX_CONTENT_LENGTH=100000
```

#### MCP Client Configuration

**Claude Desktop** (`claude_desktop_config.json`):
```json
{
  "mcpServers": {
    "page-extractor": {
      "command": "node",
      "args": ["/absolute/path/to/mcp/src/index.js"],
      "env": {
        "PAGE_EXTRACTOR_BASE_URL": "http://localhost:6000",
        "SCRAPING_TIMEOUT": "300000"
      }
    }
  }
}
```

### 4. Running in Production

#### As a System Service (Linux/macOS)

Create a systemd service file `/etc/systemd/system/page-extractor-mcp.service`:

```ini
[Unit]
Description=Page Extractor MCP Server
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/mcp
ExecStart=/usr/bin/node src/index.js
Restart=always
RestartSec=10
Environment=PAGE_EXTRACTOR_BASE_URL=http://localhost:6000
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable page-extractor-mcp
sudo systemctl start page-extractor-mcp
```

#### Using PM2 (Process Manager)

```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'page-extractor-mcp',
    script: 'src/index.js',
    cwd: '/path/to/mcp',
    env: {
      NODE_ENV: 'production',
      PAGE_EXTRACTOR_BASE_URL: 'http://localhost:6000'
    }
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 5. Docker Deployment

Create `Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/

# Make executable
RUN chmod +x src/index.js

# Set environment variables
ENV PAGE_EXTRACTOR_BASE_URL=http://host.docker.internal:6000
ENV NODE_ENV=production

# Expose stdio for MCP communication
CMD ["node", "src/index.js"]
```

Build and run:
```bash
docker build -t page-extractor-mcp .
docker run -d --name page-extractor-mcp page-extractor-mcp
```

### 6. Monitoring and Logging

#### Health Checks

Create a health check script:

```bash
#!/bin/bash
# health-check.sh

echo '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' | \
  timeout 10 node src/index.js > /dev/null 2>&1

if [ $? -eq 0 ]; then
  echo "MCP Server is healthy"
  exit 0
else
  echo "MCP Server is unhealthy"
  exit 1
fi
```

#### Logging

For production logging, modify `src/index.js` to add structured logging:

```javascript
// Add at the top
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Use logger.info(), logger.error() instead of console.log()
```

### 7. Security Considerations

#### Network Security

- Run the MCP server on localhost only
- Use firewall rules to restrict access
- Consider VPN for remote access

#### Process Security

- Run as non-root user
- Use process isolation (containers, chroot)
- Limit resource usage

#### Input Validation

- All URLs are validated
- Content length is limited
- Timeout prevents DoS

### 8. Performance Tuning

#### Memory Management

```javascript
// Add to src/index.js
process.on('warning', (warning) => {
  console.warn(warning.name);
  console.warn(warning.message);
  console.warn(warning.stack);
});

// Monitor memory usage
setInterval(() => {
  const used = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(used.rss / 1024 / 1024) + 'MB',
    heapTotal: Math.round(used.heapTotal / 1024 / 1024) + 'MB',
    heapUsed: Math.round(used.heapUsed / 1024 / 1024) + 'MB'
  });
}, 30000);
```

#### Connection Pooling

For high-volume usage, consider implementing connection pooling for the HTTP client.

### 9. Backup and Recovery

#### Configuration Backup

```bash
# Backup configuration
tar -czf mcp-backup-$(date +%Y%m%d).tar.gz \
  src/ package.json README.md examples/
```

#### Disaster Recovery

- Keep configuration in version control
- Document deployment procedures
- Test recovery procedures regularly

### 10. Maintenance

#### Updates

```bash
# Update dependencies
npm update

# Test after updates
npm test

# Restart service
sudo systemctl restart page-extractor-mcp
```

#### Monitoring

- Monitor CPU and memory usage
- Check error logs regularly
- Monitor response times
- Set up alerts for failures

## Troubleshooting Production Issues

### Common Problems

1. **High Memory Usage**: Increase `MAX_CONTENT_LENGTH` limit
2. **Slow Responses**: Tune `SCRAPING_TIMEOUT`
3. **Connection Errors**: Check main server availability
4. **Process Crashes**: Check logs and restart policies

### Debug Mode

Enable debug logging:
```bash
DEBUG=* node src/index.js
```

### Performance Monitoring

Use tools like:
- `htop` for system resources
- `node --inspect` for Node.js debugging
- Application monitoring services
