# Usage Examples

## Basic Web Scraping

### Scrape a Simple Page

```json
{
  "name": "scrape_page",
  "arguments": {
    "url": "https://example.com"
  }
}
```

### Scrape with Query Focus

```json
{
  "name": "scrape_page", 
  "arguments": {
    "url": "https://news.ycombinator.com",
    "query": "top stories and comments",
    "mode": "normal"
  }
}
```

### Beast Mode Scraping

```json
{
  "name": "scrape_page",
  "arguments": {
    "url": "https://complex-spa-website.com",
    "mode": "beast"
  }
}
```

## Streaming Scraping

### Basic Streaming

```json
{
  "name": "scrape_page_stream",
  "arguments": {
    "url": "https://example.com"
  }
}
```

This will show progress updates like:
```
🔗 Connected: Stream connected, starting scraping process...
📊 Progress: Navigating to URL...
📊 Progress: Waiting for page load...
📊 Progress: Extracting content...
📊 Progress: Converting to markdown...
✅ Successfully scraped with streaming: https://example.com

Progress Updates:
🔗 Connected: Stream connected, starting scraping process...
📊 Progress: Navigating to URL...
📊 Progress: Page loaded successfully
📊 Progress: Extracting main content
📊 Progress: Processing interactive elements
📊 Progress: Converting HTML to markdown
📊 Progress: Cleaning up markdown format

Final Result:
# Example Domain

This domain is for use in illustrative examples...
```

### Streaming with Custom Server

```json
{
  "name": "scrape_page_stream",
  "arguments": {
    "url": "https://example.com",
    "baseUrl": "http://localhost:3000"
  }
}
```

## Advanced Examples

### E-commerce Product Page

```json
{
  "name": "scrape_page",
  "arguments": {
    "url": "https://shop.example.com/product/123",
    "query": "product details, price, reviews",
    "mode": "beast"
  }
}
```

### News Article with Comments

```json
{
  "name": "scrape_page_stream",
  "arguments": {
    "url": "https://news.example.com/article/123",
    "query": "article content and user comments",
    "mode": "beast"
  }
}
```

### Documentation Site

```json
{
  "name": "scrape_page",
  "arguments": {
    "url": "https://docs.example.com/api/reference",
    "query": "API documentation and code examples"
  }
}
```

## Error Handling Examples

### Invalid URL

```json
{
  "name": "scrape_page",
  "arguments": {
    "url": "not-a-valid-url"
  }
}
```

Response:
```json
{
  "content": [
    {
      "type": "text",
      "text": "Error: Invalid URL format"
    }
  ],
  "isError": true
}
```

### Server Unavailable

```json
{
  "name": "scrape_page",
  "arguments": {
    "url": "https://example.com",
    "baseUrl": "http://localhost:9999"
  }
}
```

Response:
```json
{
  "content": [
    {
      "type": "text", 
      "text": "Error: Failed to scrape page: Connection refused"
    }
  ],
  "isError": true
}
```

## Integration Examples

### Claude Desktop

1. Add to your Claude Desktop config:

```json
{
  "mcpServers": {
    "page-extractor": {
      "command": "node",
      "args": ["/path/to/mcp/src/index.js"],
      "env": {
        "PAGE_EXTRACTOR_BASE_URL": "http://localhost:6000"
      }
    }
  }
}
```

2. Use in Claude:

```
Please scrape https://example.com and summarize the main content.
```

Claude will use the `scrape_page` tool automatically.

### Custom MCP Client

```javascript
import { Client } from '@modelcontextprotocol/sdk/client/index.js';

const client = new Client({
  name: "my-app",
  version: "1.0.0"
});

// Connect to the MCP server
await client.connect(transport);

// List available tools
const tools = await client.listTools();

// Call scrape_page tool
const result = await client.callTool({
  name: 'scrape_page',
  arguments: {
    url: 'https://example.com',
    mode: 'normal'
  }
});

console.log(result.content[0].text);
```

## Performance Tips

1. **Use Normal Mode First**: Start with `mode: "normal"` for faster results
2. **Specific Queries**: Use the `query` parameter to focus on relevant content
3. **Streaming for Long Operations**: Use `scrape_page_stream` for complex pages
4. **Timeout Configuration**: Adjust `SCRAPING_TIMEOUT` for slow sites

## Common Use Cases

### Content Research
- Blog posts and articles
- Documentation and guides
- News and updates

### Data Extraction
- Product information
- Contact details
- Pricing data

### Monitoring
- Website changes
- Content updates
- Availability checks
