#!/bin/bash

# Page Extractor MCP Server Setup Script

set -e

echo "🚀 Setting up Page Extractor MCP Server..."

# Check Node.js version
echo "📋 Checking Node.js version..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18.0.0 or higher."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please upgrade to $REQUIRED_VERSION or higher."
    exit 1
fi

echo "✅ Node.js version $NODE_VERSION is compatible"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Make scripts executable
chmod +x src/index.js
chmod +x test/test-server.js

# Test the installation
echo "🧪 Testing installation..."
npm test

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "📖 Next steps:"
echo "1. Make sure your main scraping server is running on localhost:6000"
echo "2. Configure your MCP client to use this server"
echo "3. Example Claude Desktop config is in examples/claude-desktop-config.json"
echo ""
echo "🎯 Usage:"
echo "  npm start    # Start the MCP server"
echo "  npm test     # Test the server"
echo ""
echo "📍 Current directory: $(pwd)"
echo "📄 Main server file: $(pwd)/src/index.js"
