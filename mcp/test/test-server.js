#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test the MCP server by simulating MCP client interactions
async function testMCPServer() {
  console.log('🧪 Testing Page Extractor MCP Server...\n');

  const serverPath = join(__dirname, '../src/index.js');
  
  // Start the MCP server
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
  });

  let serverOutput = '';
  let serverError = '';

  server.stdout.on('data', (data) => {
    serverOutput += data.toString();
  });

  server.stderr.on('data', (data) => {
    serverError += data.toString();
    console.log('Server stderr:', data.toString());
  });

  // Wait a moment for server to start
  await new Promise(resolve => setTimeout(resolve, 1000));

  try {
    // Test 1: List tools
    console.log('📋 Test 1: Listing available tools...');
    const listToolsRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    };

    server.stdin.write(JSON.stringify(listToolsRequest) + '\n');

    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('✅ List tools test completed\n');

    // Test 2: Test scrape_page tool (this will fail if the main server isn't running)
    console.log('🌐 Test 2: Testing scrape_page tool...');
    const scrapeRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'scrape_page',
        arguments: {
          url: 'https://example.com',
          mode: 'normal',
          baseUrl: 'http://localhost:6000'
        }
      }
    };

    server.stdin.write(JSON.stringify(scrapeRequest) + '\n');

    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('✅ Scrape page test completed\n');

    // Test 3: Test scrape_page_stream tool
    console.log('📡 Test 3: Testing scrape_page_stream tool...');
    const scrapeStreamRequest = {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'scrape_page_stream',
        arguments: {
          url: 'https://example.com',
          mode: 'normal',
          baseUrl: 'http://localhost:6000'
        }
      }
    };

    server.stdin.write(JSON.stringify(scrapeStreamRequest) + '\n');

    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 10000));

    console.log('✅ Scrape page stream test completed\n');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Clean up
    server.kill();
    
    console.log('📊 Test Results:');
    console.log('Server Output:', serverOutput);
    if (serverError) {
      console.log('Server Errors:', serverError);
    }
    
    console.log('\n🎯 Test completed!');
    console.log('Note: Some tests may fail if the main scraping server (localhost:6000) is not running.');
    console.log('To test fully, start your main server first with: npm start');
  }
}

// Run tests
testMCPServer().catch(console.error);
