# Page Extractor MCP Server

A production-ready Model Context Protocol (MCP) server that provides web scraping capabilities with real-time streaming progress updates.

## Features

- 🌐 **Web Scraping**: Extract content from any web page as clean markdown
- 📡 **Streaming Support**: Real-time progress updates via Server-Sent Events (SSE)
- ⚡ **Dual Modes**: Normal (fast) and Beast (thorough) scraping modes
- 🔧 **Production Ready**: Robust error handling, timeouts, and validation
- 🎯 **MCP Compatible**: Works with any MCP-supported application

## Quick Start

### Prerequisites

1. **Main Scraping Server**: Ensure your main scraping server is running on `localhost:6000`
   ```bash
   # In your main project directory
   npm start
   ```

2. **Node.js**: Version 18.0.0 or higher

### Installation

```bash
cd mcp
npm install
```

### Usage

#### As MCP Server (Recommended)

The server runs on stdio and communicates via JSON-RPC:

```bash
npm start
```

#### Testing

Test the server functionality:

```bash
npm test
```

## Available Tools

### 1. `scrape_page`

Synchronous web scraping that returns results immediately.

**Parameters:**
- `url` (required): The URL to scrape
- `query` (optional): Focus extraction on specific content
- `mode` (optional): `"normal"` (default) or `"beast"`
- `baseUrl` (optional): Base URL of scraping service (default: `http://localhost:6000`)

**Example:**
```json
{
  "name": "scrape_page",
  "arguments": {
    "url": "https://example.com",
    "query": "main content",
    "mode": "normal"
  }
}
```

### 2. `scrape_page_stream`

Streaming web scraping with real-time progress updates.

**Parameters:** Same as `scrape_page`

**Features:**
- Live progress updates during scraping
- Real-time status messages
- Streaming results via SSE

## Configuration

### Environment Variables

- `PAGE_EXTRACTOR_BASE_URL`: Base URL for the scraping service (default: `http://localhost:6000`)
- `SCRAPING_TIMEOUT`: Timeout in milliseconds (default: `300000` - 5 minutes)
- `MAX_CONTENT_LENGTH`: Maximum content length to return (default: `100000`)

### Example Configuration

```bash
export PAGE_EXTRACTOR_BASE_URL="http://localhost:6000"
export SCRAPING_TIMEOUT="600000"
export MAX_CONTENT_LENGTH="200000"
```

## Integration with MCP Clients

### Claude Desktop

Add to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "page-extractor": {
      "command": "node",
      "args": ["/path/to/mcp/src/index.js"],
      "env": {
        "PAGE_EXTRACTOR_BASE_URL": "http://localhost:6000"
      }
    }
  }
}
```

### Other MCP Clients

The server follows the standard MCP protocol and should work with any MCP-compatible client.

## API Reference

### Scraping Modes

- **Normal Mode**: Fast scraping with basic content extraction
- **Beast Mode**: Thorough scraping with AI-powered interaction detection

### Response Format

#### Successful Response
```json
{
  "content": [
    {
      "type": "text",
      "text": "Successfully scraped: https://example.com\n\nMarkdown Content:\n..."
    }
  ]
}
```

#### Error Response
```json
{
  "content": [
    {
      "type": "text", 
      "text": "Error: Failed to scrape page: Connection refused"
    }
  ],
  "isError": true
}
```

### Streaming Progress Updates

When using `scrape_page_stream`, you'll receive progress updates:

```
🔗 Connected: Stream connected, starting scraping process...
📊 Progress: Navigating to URL...
📊 Progress: Extracting content...
📊 Progress: Converting to markdown...
✅ Final Result: [Extracted content]
```

## Error Handling

The server includes comprehensive error handling:

- **Connection Errors**: When the main scraping server is unavailable
- **Timeout Errors**: When scraping takes too long
- **Validation Errors**: When invalid parameters are provided
- **Streaming Errors**: When SSE connection fails

## Development

### Project Structure

```
mcp/
├── src/
│   ├── index.js      # Main MCP server
│   └── config.js     # Configuration
├── test/
│   └── test-server.js # Test script
├── package.json
└── README.md
```

### Testing Locally

1. Start your main scraping server:
   ```bash
   npm start  # In main project directory
   ```

2. Test the MCP server:
   ```bash
   cd mcp
   npm test
   ```

### Debugging

Enable debug output:
```bash
DEBUG=* npm start
```

## Troubleshooting

### Common Issues

1. **"Connection refused"**: Ensure your main scraping server is running on port 6000
2. **"Timeout"**: Increase `SCRAPING_TIMEOUT` for complex pages
3. **"Invalid URL"**: Check URL format and accessibility

### Logs

The server logs to stderr (visible in MCP client logs):
```
Page Extractor MCP server running on stdio
```

## License

MIT License - see main project for details.

## Support

For issues and questions, please refer to the main project documentation or create an issue in the repository.
